using App.Base.Middleware;
using App.ECommerce.Helpers;
using App.ECommerce.Helpers.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Dtos.ResultDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Setting;
using App.ECommerce.Units.Enums;

using log4net;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

using MongoDB.Driver;

using static App.ECommerce.Resource.Enums.AffiliationEnum;

using Action = App.ECommerce.Resource.Model.Action;

namespace App.ECommerce.Controllers.API.Affiliate
{
    [ApiController]
    [Produces("application/json")]
    [Route(RoutePrefix.API_AFFILIATION)]
    [ApiExplorerSettings(GroupName = "affiliation-v1")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
    public class AffiliationPartnerController : BaseController
    {
        private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(AffiliationPartnerController));
        private readonly IAffiliationPartnerRepository _affiliationPartnerRepository;
        private readonly IPartnerRepository _partnerRepository;
        private readonly IUserRepository _useRepository;
        private readonly IShopRepository _shopRepository;
        private readonly ICommissionsConfigRepository _commissionsConfigRepository;
        private readonly ICommissionsConfigHelpers _commissionsConfigHelpers;

        public AffiliationPartnerController(IStringLocalizer localizer,
            IAffiliationPartnerRepository affiliationPartner,
            IPartnerRepository partnerRepository,
            IShopRepository shopRepository,
            ICommissionsConfigRepository commissionsConfigRepository,
            IUserRepository userRepository,
            ICommissionsConfigHelpers commissionsConfigHelpers
            )
            : base(localizer)
        {
            _affiliationPartnerRepository = affiliationPartner;
            _partnerRepository = partnerRepository;
            _shopRepository = shopRepository;
            _commissionsConfigRepository = commissionsConfigRepository;
            _useRepository = userRepository;
            _commissionsConfigHelpers = commissionsConfigHelpers;
        }
        /// <summary>
        /// Get list partners (Lấy danh sách đối tác (Quản lý đối tác))
        /// </summary>
        /// <param name="model"></param>
        /// <param name="skip"></param>
        /// <param name="limit"></param>
        /// <returns>Result list cart for Partner</returns>
        // GET: api/affiliation/Partners
        [HttpGet("Partners")]
        public async Task<IActionResult> GetPartners([FromQuery] PartnerInputDto model)
        {
            try
            {
                string partnerId = GetUserIdAuth();
                Partner? partner = await _partnerRepository.FindByPartnerId(partnerId);
                if (partner == null)
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"),
                        this.ControllerContext));

                Shop? shop = _shopRepository.FindByShopId(model.ShopId);
                if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
                if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

                if (!model.IsValidDate())
                    return ResponseUnauthorized(new CustomBadRequest(localizer("DATE_RANGE_VALID"),
                           this.ControllerContext));

                var commissionConfig = await _commissionsConfigRepository.FindCommissionsConfig(model.ShopId);

                if (commissionConfig == null)
                    return ResponseBadRequest(new CustomBadRequest(localizer("NOT_FOUND_COMMISSIONS_CONFIG"), this.ControllerContext));

                PagingResult<User> partners = await _useRepository.ListUserFilteredByDate(model, model.ShopId);
                List<PartnersResultDto> listpartners = _mapper.Map<List<PartnersResultDto>>(partners.Result);

                if (listpartners.Any(x => !string.IsNullOrEmpty(x.ReferrerCode)))
                {
                    var referrers = await _useRepository.FindReferers(model.ShopId);
                    foreach (var item in listpartners)
                    {
                        item.ReferrerName = referrers?.FirstOrDefault(r => r.ReferralCode == item.ReferrerCode)?.Fullname;
                    }
                }

                foreach (var item in listpartners)
                {
                    var matchedUser = partners.Result.FirstOrDefault(u => u.UserId == item.UserId);
                    if (matchedUser?.ApprovalDate != null)
                    {
                        item.AffiliationExpireAt = _commissionsConfigHelpers.CalculateExpirationDate(commissionConfig?.AdvancedCommissionsConfig?.PartnerCommExpiry ?? 1, matchedUser.ApprovalDate ?? DateTime.Now);
                    }
                }
                return ResponseData(new { data = listpartners, isAutoApproved = commissionConfig.AdvancedCommissionsConfig.IsAutoApproved, total = partners.Total });
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.Partner,
                    Action = LogActionEnum.Load,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/affiliationpartner/partners",
                    Message = $"Error Get Partners",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/Partner", ex, model);
            }
        }
        /// <summary>
        /// Get detail partner (Lấy thông tin chi tiết đối tác)
        /// </summary>
        /// <param name="id"></param>
        /// <returns>The result detail partner</returns>
        // GET: api/affiliation/Partner
        [HttpGet("Partner")]
        public async Task<IActionResult> GetPartner(string id)
        {
            try
            {
                string partnerId = GetUserIdAuth();
                Partner? partner = await _partnerRepository.FindByPartnerId(partnerId);
                if (partner == null)
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

                User user = await _useRepository.FindUserByIdAsync(id);
                if (user == null)
                    return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_NOT_FOUND"), this.ControllerContext));

                var data = _mapper.Map<PartnerDetailDto>(user);

                return ResponseData(new { Data = data });
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.Partner,
                    Action = LogActionEnum.Load,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/GetPartner/{id}",
                    Message = $"Error Get Partner Detail",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/GetPartner/{id}", ex);
            }
        }
        /// <summary>
        /// Get referers  (Lấy danh sách người giới thiệu của đối tác)
        /// </summary>
        /// <param name="ShopId"></param>
        /// <param name="UserId"></param>
        /// <returns>The list of referers</returns>
        // GET: api/affiliation/referers
        [HttpGet("referers")]
        public async Task<IActionResult> GetReferers(string ShopId, string UserId)
        {
            try
            {
                string partnerId = GetUserIdAuth();
                Partner? partner = await _partnerRepository.FindByPartnerId(partnerId);
                if (partner == null)
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"),
                        this.ControllerContext));

                Shop? shop = _shopRepository.FindByShopId(ShopId);
                if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
                if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

                // Lấy danh sách referers
                var referers = await _useRepository.FindReferers(ShopId, UserId);
                if (referers == null)
                    return ResponseBadRequest(new CustomBadRequest(localizer("NO_REFERERS_FOUND"), this.ControllerContext));

                var refererDtos = _mapper.Map<List<RefererDto>>(referers);

                return ResponseData(new { data = refererDtos });
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.Partner,
                    Action = LogActionEnum.Load,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/affiliationpartner/partners/{ShopId}/referers",
                    Message = $"Error Get GetReferers",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/partners/{ShopId}/referers", ex);
            }
        }
        /// <summary>
        /// Update partner details (Cập nhật thông tin đối tác)
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="dto">Data update</param>
        /// <returns>Updated partner details</returns>
        // PUT: Put /affiliation/UpdatePartner
        [HttpPut("UpdatePartner")]
        public async Task<IActionResult> UpdatePartner(string id, [FromBody] UpdatePartnerDto dto)
        {
            try
            {
                string partnerId = GetUserIdAuth();
                Partner? partner = await _partnerRepository.FindByPartnerId(partnerId);
                if (partner == null)
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

                User user = await _useRepository.FindUserByIdAsync(id);
                if (user == null)
                    return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_NOT_FOUND"), this.ControllerContext));

                //bool checkEmail = _useRepository.IsEmailExistOnShop(dto.Email, user.ShopId, user.UserId);
                //if(checkEmail) return ResponseBadRequest(new CustomBadRequest(localizer("AUTH_EMAIL_REGISTERED_USER"), this.ControllerContext));

                bool checkPhone = _useRepository.IsPhoneExistOnShop(dto.PhoneNumber, user.ShopId, user.UserId);
                if (checkPhone) return ResponseBadRequest(new CustomBadRequest(localizer("AUTH_PHONE_REGISTERED_USER"), this.ControllerContext));

                if (dto.AffiliationStatus != user.AffiliationStatus)
                {
                    var canAffordPurchase = await _affiliationPartnerRepository.CanUserAffordPurchaseAsync(id, user.ShopId);
                    if (!canAffordPurchase)
                        return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_INSUFFICIENT_SPENDING"), this.ControllerContext));
                }

                // Xử lý ReferrerCode
                if (!string.IsNullOrWhiteSpace(dto.ReferrerCode))
                {
                    var newParentId = _useRepository.FindByReferralCode(dto.ReferrerCode, user.ShopId);
                    if (newParentId == null || newParentId.UserId == user.UserId)
                    {
                        return ResponseBadRequest(new CustomBadRequest(localizer("REFERRER_CODE_INVALID"), this.ControllerContext));
                    }

                    // Thêm kiểm tra vòng lặp
                    if (_affiliationPartnerRepository.HasReferralCycle(user.UserId, newParentId.UserId))
                    {
                        return ResponseBadRequest(new CustomBadRequest(localizer("REFERRER_CODE_INVALID_CYCLE"), this.ControllerContext));
                    }

                    user.ParentId = newParentId.UserId;
                }
                // Nếu ReferrerCode là null/empty, giữ nguyên ParentId hiện tại

                var model = _mapper.Map(dto, user);
                _useRepository.UpdateUser(model);

                return Ok(new { message = "Affiliation partner was updated successfully" });
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.Partner,
                    Action = LogActionEnum.Load,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/affiliationpartner/partners/UpdatePartner/{id}",
                    Message = $"Error UpdatePartner {id}",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/UpdatePartner/{id}", ex);
            }
        }
        /// <summary>
        /// Approval partner (Duyệt đối tác)
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>Updated partner details</returns>
        // PUT: api/partner/affiliation/Perform
        [HttpPut("Approval")]
        public async Task<IActionResult> Approval(string id)
        {
            try
            {
                string partnerId = GetUserIdAuth();
                Partner? partner = await _partnerRepository.FindByPartnerId(partnerId);
                if (partner == null)
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

                User user = await _useRepository.FindUserByIdAsync(id);
                if (user == null)
                    return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_NOT_FOUND"), this.ControllerContext));

                if (user.AffiliationStatus != AffiliationTypeStatus.Actived)
                {

                    var configCommission = await _commissionsConfigRepository.FindCommissionsConfig(user.ShopId);
                    if (configCommission == null)
                        return ResponseBadRequest(new CustomBadRequest(localizer("NOT_FOUND_COMMISSIONS_CONFIG"), this.ControllerContext));
                    if (!configCommission.IsActive)
                    {
                        return ResponseBadRequest(new CustomBadRequest(localizer("COMMISSION_CONFIG_NOT_ACTIVE"), this.ControllerContext));
                    }
                    var userAffordPurchase = await _affiliationPartnerRepository.CanUserAffordPurchaseAsync(id, user.ShopId);
                    if (!userAffordPurchase)
                        return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_INSUFFICIENT_SPENDING"), this.ControllerContext));

                    user.AffiliationStatus = AffiliationTypeStatus.Actived;
                    user.ApprovalDate = DateTime.Now;
                    user.AffiliationExpireDate = _commissionsConfigHelpers.CalculateExpirationDate(configCommission?.AdvancedCommissionsConfig?.PartnerCommExpiry ?? 1, user.ApprovalDate ?? DateTime.Now);
                    _useRepository.UpdateUser(user);
                }

                return Ok(new { message = "Approval successful" });
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.Partner,
                    Action = LogActionEnum.Approve,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/affiliationpartner/Approval/{id}",
                    Message = $"Error Approval {id}",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/UpdatePartner/{id}", ex);
            }
        }
        /// <summary>
        /// Get quick report for a partner (Lấy báo cáo nhanh của đối tác)
        /// </summary>
        /// <param name="userId"></param>
        /// <returns>The quick report data</returns>
        // GET: api/affiliation/partners/{partnerId}/quickReport
        [HttpGet("partners/{userId}/quickReport")]
        public async Task<IActionResult> GetQuickReport(string userId)
        {
            try
            {
                string partnerId = GetUserIdAuth();
                Partner? partner = await _partnerRepository.FindByPartnerId(partnerId);
                if (partner == null)
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

                User user = _useRepository.FindByUserId(userId);
                if (user == null)
                    return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_NOT_FOUND"), this.ControllerContext));

                var quickReport = await _affiliationPartnerRepository.GetQuickReport(userId, user.ShopId);

                return ResponseData(new { data = quickReport });
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.Partner,
                    Action = LogActionEnum.Load,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/partners/{userId}/quickReport",
                    Message = $"Error getting quick report",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/partners/{userId}/quickReport", ex);
            }
        }
        /// <summary>
        /// Export excel (Xuất excel danh sách đối tác (Quản lý đối tác))
        /// </summary>
        /// <param name="model"></param>
        /// <returns>Result excel file </returns>
        // GET: api/AffiliationPartner/Partners
        [HttpGet("excelFile")]
        public async Task<IActionResult> ExcelFile([FromQuery] PartnerInputDto model)
        {
            try
            {
                string partnerId = GetUserIdAuth();
                Partner? partner = await _partnerRepository.FindByPartnerId(partnerId);
                if (partner == null)
                    return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"),
                        this.ControllerContext));

                Shop? shop = _shopRepository.FindByShopId(model.ShopId);
                if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
                if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

                if (!model.IsValidDate())
                    return ResponseUnauthorized(new CustomBadRequest(localizer("DATE_RANGE_VALID"),
                           this.ControllerContext));

                PagingResult<User> partners = await _useRepository.ListExcelUser(model, model.ShopId);
                List<PartnersExcelResultDto> listpartners = _mapper.Map<List<PartnersExcelResultDto>>(partners.Result);

                if (listpartners.Any(x => !string.IsNullOrEmpty(x.ReferrerCode)))
                {
                    var referrers = await _useRepository.FindReferers(model.ShopId);

                    foreach (var item in listpartners)
                    {
                        item.ReferrerName = referrers?.FirstOrDefault(r => r.ReferralCode == item.ReferrerCode)?.Fullname;
                    }
                }

                byte[] excelFile = ExcelHelper.ExportToExcel(listpartners, "Partners");

                return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Partners.xlsx");
            }
            catch (Exception ex)
            {
                LogEvent(new EventLogDto
                {
                    RefId = "",
                    RefType = TypeFor.Partner,
                    Action = LogActionEnum.Export,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.AFFILIATION}/excelFile",
                    Message = $"Error Export Partners",
                    Exception = ex,
                    DataObject = null
                });

                return LogExceptionEvent(_log4net, $"{RoutePrefix.AFFILIATION}/excelFile", ex, model);
            }
        }

    }
}
